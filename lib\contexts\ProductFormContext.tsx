'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import {
  ProductInfoFormData,
  SpecificationsFormData,
  VariationsFormData,
  CreateProductData
} from '@/lib/types/products';

interface ProductFormContextType {
  // Form data
  productInfo: ProductInfoFormData;
  specifications: SpecificationsFormData;
  variations: VariationsFormData;
  
  // Update functions
  updateProductInfo: (data: Partial<ProductInfoFormData>) => void;
  updateSpecifications: (data: Partial<SpecificationsFormData>) => void;
  updateVariations: (data: Partial<VariationsFormData>) => void;
  
  // Utility functions
  getCompleteFormData: () => CreateProductData;
  resetForm: () => void;
  isStepComplete: (step: 'product-info' | 'specifications' | 'variations') => boolean;
  
  // Validation
  validateProductInfo: () => string[];
  validateSpecifications: () => string[];
  validateVariations: () => string[];
}

const ProductFormContext = createContext<ProductFormContextType | undefined>(undefined);

// Initial form data
const initialProductInfo: ProductInfoFormData = {
  name: '',
  brand: '',
  description: '',
  basePrice: 0,
  category: '',
  gender: 'Unisex',
  productImages: [],
  highlights: [],
  tags: [],
};

const initialSpecifications: SpecificationsFormData = {
  material: '',
  fitType: '',
  care: '',
  origin: '',
  season: [],
  occasion: [],
};

const initialVariations: VariationsFormData = {
  variations: [{
    color: '',
    size: '',
    quantity: 1,
    price: 0,
    salePrice: undefined,
    saleStartDate: '',
    saleEndDate: '',
  }],
  relatedCategories: [],
};

export function ProductFormProvider({ children }: { children: ReactNode }) {
  const [productInfo, setProductInfo] = useState<ProductInfoFormData>(initialProductInfo);
  const [specifications, setSpecifications] = useState<SpecificationsFormData>(initialSpecifications);
  const [variations, setVariations] = useState<VariationsFormData>(initialVariations);

  const updateProductInfo = useCallback((data: Partial<ProductInfoFormData>) => {
    console.log('Updating product info with:', data);
    setProductInfo(prev => {
      const updated = { ...prev, ...data };
      console.log('Updated product info:', updated);
      return updated;
    });
  }, []);

  const updateSpecifications = useCallback((data: Partial<SpecificationsFormData>) => {
    console.log('Updating specifications with:', data);
    setSpecifications(prev => {
      const updated = { ...prev, ...data };
      console.log('Updated specifications:', updated);
      return updated;
    });
  }, []);

  const updateVariations = useCallback((data: Partial<VariationsFormData>) => {
    console.log('Updating variations with:', data);
    setVariations(prev => {
      const updated = { ...prev, ...data };
      console.log('Updated variations:', updated);
      return updated;
    });
  }, []);

  const getCompleteFormData = useCallback((): CreateProductData => {
    console.log('=== Context State Debug ===');
    console.log('productInfo:', productInfo);
    console.log('specifications:', specifications);
    console.log('variations:', variations);

    const completeData = {
      name: productInfo.name,
      brand: productInfo.brand,
      description: productInfo.description,
      basePrice: productInfo.basePrice,
      category: productInfo.category,
      gender: productInfo.gender,
      productImages: productInfo.productImages,
      highlights: productInfo.highlights,
      tags: productInfo.tags,
      specifications: specifications,
      variations: variations.variations,
      relatedCategories: variations.relatedCategories,
    };

    console.log('Assembled complete data:', completeData);
    console.log('=== End Context State Debug ===');

    return completeData;
  }, [productInfo, specifications, variations]);

  const resetForm = useCallback(() => {
    setProductInfo(initialProductInfo);
    setSpecifications(initialSpecifications);
    setVariations(initialVariations);
  }, []);

  const isStepComplete = useCallback((step: 'product-info' | 'specifications' | 'variations'): boolean => {
    switch (step) {
      case 'product-info':
        return !!(
          productInfo.name &&
          productInfo.brand &&
          productInfo.description &&
          productInfo.basePrice > 0 &&
          productInfo.category &&
          productInfo.gender &&
          productInfo.productImages.length > 0
        );
      case 'specifications':
        return !!(
          specifications.material ||
          specifications.fitType ||
          specifications.care ||
          specifications.origin ||
          specifications.season.length > 0 ||
          specifications.occasion.length > 0
        );
      case 'variations':
        return variations.variations.length > 0 &&
               variations.variations.every(v => v.color && v.size && v.quantity > 0 && v.price > 0);
      default:
        return false;
    }
  }, [productInfo, specifications, variations]);

  const validateProductInfo = useCallback((): string[] => {
    const errors: string[] = [];

    if (!productInfo.name.trim()) errors.push('Product name is required');
    if (!productInfo.brand.trim()) errors.push('Brand is required');
    if (!productInfo.description.trim()) errors.push('Description is required');
    if (productInfo.basePrice <= 0) errors.push('Base price must be greater than 0');
    if (!productInfo.category) errors.push('Category is required');
    if (!productInfo.gender) errors.push('Gender is required');
    if (productInfo.productImages.length === 0) errors.push('At least one product image is required');

    return errors;
  }, [productInfo]);

  const validateSpecifications = useCallback((): string[] => {
    const errors: string[] = [];

    // Specifications are generally optional, but we can add specific validation if needed
    // For now, we'll just ensure at least one specification is provided
    const hasAnySpec = !!(
      specifications.material ||
      specifications.fitType ||
      specifications.care ||
      specifications.origin ||
      specifications.season.length > 0 ||
      specifications.occasion.length > 0
    );

    if (!hasAnySpec) {
      errors.push('At least one specification is required');
    }

    return errors;
  }, [specifications]);

  const validateVariations = useCallback((): string[] => {
    const errors: string[] = [];

    if (variations.variations.length === 0) {
      errors.push('At least one product variation is required');
      return errors;
    }

    variations.variations.forEach((variation, index) => {
      if (!variation.color.trim()) {
        errors.push(`Variation ${index + 1}: Color is required`);
      }
      if (!variation.size.trim()) {
        errors.push(`Variation ${index + 1}: Size is required`);
      }
      if (variation.quantity <= 0) {
        errors.push(`Variation ${index + 1}: Quantity must be greater than 0`);
      }
      if (variation.price <= 0) {
        errors.push(`Variation ${index + 1}: Price must be greater than 0`);
      }
      if (variation.salePrice && variation.salePrice >= variation.price) {
        errors.push(`Variation ${index + 1}: Sale price must be less than regular price`);
      }
    });

    return errors;
  }, [variations]);

  const value: ProductFormContextType = {
    productInfo,
    specifications,
    variations,
    updateProductInfo,
    updateSpecifications,
    updateVariations,
    getCompleteFormData,
    resetForm,
    isStepComplete,
    validateProductInfo,
    validateSpecifications,
    validateVariations,
  };

  return (
    <ProductFormContext.Provider value={value}>
      {children}
    </ProductFormContext.Provider>
  );
}

export function useProductForm() {
  const context = useContext(ProductFormContext);
  if (context === undefined) {
    throw new Error('useProductForm must be used within a ProductFormProvider');
  }
  return context;
}
