"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Plus, ChevronLeft } from "lucide-react";
import { useProductForm } from "@/lib/contexts/ProductFormContext";
import { 
  SEASON_OPTIONS, 
  OCCASION_OPTIONS, 
  FIT_TYPE_OPTIONS, 
  MATERIAL_OPTIONS 
} from "@/lib/types/products";
import { toast } from "@/hooks/use-toast";

// Form validation schema
const specificationsSchema = z.object({
  material: z.string().optional(),
  fitType: z.string().optional(),
  care: z.string().optional(),
  origin: z.string().optional(),
  season: z.array(z.string()).min(1, "At least one season is required"),
  occasion: z.array(z.string()).min(1, "At least one occasion is required"),
});

type SpecificationsFormData = z.infer<typeof specificationsSchema>;

interface SpecificationsFormProps {
  onNext: () => void;
  onBack: () => void;
}

export default function SpecificationsForm({ onNext, onBack }: SpecificationsFormProps) {
  const { specifications, updateSpecifications } = useProductForm();
  const [newSeason, setNewSeason] = useState("");
  const [newOccasion, setNewOccasion] = useState("");

  // Form setup
  const form = useForm<SpecificationsFormData>({
    resolver: zodResolver(specificationsSchema),
    defaultValues: {
      material: specifications.material || "",
      fitType: specifications.fitType || "",
      care: specifications.care || "",
      origin: specifications.origin || "",
      season: specifications.season || [],
      occasion: specifications.occasion || [],
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  // Handle season management
  const addSeason = (seasonValue: string) => {
    const currentSeasons = watch('season') || [];
    if (seasonValue && !currentSeasons.includes(seasonValue)) {
      setValue('season', [...currentSeasons, seasonValue]);
    }
  };

  const removeSeason = (index: number) => {
    const currentSeasons = watch('season') || [];
    setValue('season', currentSeasons.filter((_, i) => i !== index));
  };

  // Handle occasion management
  const addOccasion = (occasionValue: string) => {
    const currentOccasions = watch('occasion') || [];
    if (occasionValue && !currentOccasions.includes(occasionValue)) {
      setValue('occasion', [...currentOccasions, occasionValue]);
    }
  };

  const removeOccasion = (index: number) => {
    const currentOccasions = watch('occasion') || [];
    setValue('occasion', currentOccasions.filter((_, i) => i !== index));
  };

  const onSubmit = (data: SpecificationsFormData) => {
    // Update context with final data
    updateSpecifications(data);

    // Navigate to next step
    onNext();
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-bold mb-6">Product Specifications</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Material */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Material</label>
            <Controller
              name="material"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select material" />
                  </SelectTrigger>
                  <SelectContent>
                    {MATERIAL_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Fit Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Fit Type</label>
            <Controller
              name="fitType"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select fit type" />
                  </SelectTrigger>
                  <SelectContent>
                    {FIT_TYPE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          {/* Care Instructions */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Care Instructions</label>
            <Controller
              name="care"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., Machine wash cold, Hand wash only"
                />
              )}
            />
          </div>

          {/* Origin */}
          <div>
            <label className="block text-sm font-medium text-gray-900 mb-1">Origin</label>
            <Controller
              name="origin"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="e.g., Ghana, Made in Ghana"
                />
              )}
            />
          </div>
        </div>

        {/* Season */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Season <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Select value={newSeason} onValueChange={setNewSeason}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select season" />
              </SelectTrigger>
              <SelectContent>
                {SEASON_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              type="button" 
              onClick={() => {
                addSeason(newSeason);
                setNewSeason("");
              }}
              disabled={!newSeason}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('season')?.map((season, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {season}
                <button
                  type="button"
                  onClick={() => removeSeason(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.season && (
            <p className="text-xs text-red-600 mt-1">{errors.season.message}</p>
          )}
        </div>

        {/* Occasion */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-900 mb-2">
            Occasion <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 mb-2">
            <Select value={newOccasion} onValueChange={setNewOccasion}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select occasion" />
              </SelectTrigger>
              <SelectContent>
                {OCCASION_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              type="button" 
              onClick={() => {
                addOccasion(newOccasion);
                setNewOccasion("");
              }}
              disabled={!newOccasion}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('occasion')?.map((occasion, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {occasion}
                <button
                  type="button"
                  onClick={() => removeOccasion(index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
          {errors.occasion && (
            <p className="text-xs text-red-600 mt-1">{errors.occasion.message}</p>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Back
          </Button>
          <Button type="submit" className="px-8">
            Next: Variations
          </Button>
        </div>
      </form>
    </div>
  );
}
