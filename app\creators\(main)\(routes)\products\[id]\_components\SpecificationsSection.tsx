"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Edit3, X, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useUpdateProductSpecifications } from "@/lib/hooks/use-products";
import {
  SEASON_OPTIONS,
  OCCASION_OPTIONS,
  FIT_TYPE_OPTIONS,
  MATERIAL_OPTIONS
} from "@/lib/types/products";

// Helper component for displaying details
const DetailItem = ({ label, value }: { label: string; value: string | number }) => (
  <div className="text-sm">
    <p className="text-gray-500">{label}</p>
    <p className="text-gray-900">{value || "Not specified"}</p>
  </div>
);

// Form validation schema - exact copy from SpecificationsForm
const specificationsSchema = z.object({
  material: z.string().optional(),
  fitType: z.string().optional(),
  care: z.string().optional(),
  origin: z.string().optional(),
  season: z.array(z.string()).min(1, "At least one season is required"),
  occasion: z.array(z.string()).min(1, "At least one occasion is required"),
});

type SpecificationsFormData = z.infer<typeof specificationsSchema>;

interface SpecificationsSectionProps {
  product: any;
}

const SpecificationsSection: React.FC<SpecificationsSectionProps> = ({ product }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newSeason, setNewSeason] = useState("");
  const [newOccasion, setNewOccasion] = useState("");

  // Use the specific update hook
  const updateSpecificationsMutation = useUpdateProductSpecifications();

  // Helper function to safely extract array data
  const getSafeArrayData = (data: any): string[] => {
    if (!data) return [];
    if (Array.isArray(data)) {
      return data.map((item: any) => {
        // If item is an object, try to extract name or convert to string
        if (typeof item === 'object' && item !== null) {
          return item.name || item.value || String(item);
        }
        return String(item);
      });
    }
    return [];
  };

  // Form setup - exact copy from SpecificationsForm
  const form = useForm<SpecificationsFormData>({
    resolver: zodResolver(specificationsSchema),
    defaultValues: {
      material: product.specifications?.material || "",
      fitType: product.specifications?.fitType || "",
      care: product.specifications?.care || "",
      origin: product.specifications?.origin || "",
      season: getSafeArrayData(product.specifications?.season),
      occasion: getSafeArrayData(product.specifications?.occasion),
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  // Handle season management
  const addSeason = (seasonValue: string) => {
    const currentSeasons = watch('season') || [];
    if (seasonValue && !currentSeasons.includes(seasonValue)) {
      setValue('season', [...currentSeasons, seasonValue]);
    }
  };

  const removeSeason = (index: number) => {
    const currentSeasons = watch('season') || [];
    setValue('season', currentSeasons.filter((_, i) => i !== index));
  };

  // Handle occasion management
  const addOccasion = (occasionValue: string) => {
    const currentOccasions = watch('occasion') || [];
    if (occasionValue && !currentOccasions.includes(occasionValue)) {
      setValue('occasion', [...currentOccasions, occasionValue]);
    }
  };

  const removeOccasion = (index: number) => {
    const currentOccasions = watch('occasion') || [];
    setValue('occasion', currentOccasions.filter((_, i) => i !== index));
  };

  const toggleEdit = () => setIsEditing(!isEditing);

  const onSubmit = async (data: SpecificationsFormData) => {
    try {
      await updateSpecificationsMutation.mutateAsync({
        productId: product._id,
        specifications: {
          mainMaterial: data.material,
          fitType: data.fitType,
          care: data.care,
          origin: data.origin,
          season: data.season,
          occasion: data.occasion,
        },
      });
      toggleEdit();
    } catch (error) {
      // Error is handled by the mutation hook
      console.error('Specifications update failed:', error);
    }
  };

  if (isEditing) {
    return (
      <div className="w-full">
        <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-bold">Product Specifications</h2>
            <Button variant="ghost" onClick={toggleEdit}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Material */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Material</label>
              <Controller
                name="material"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select material" />
                    </SelectTrigger>
                    <SelectContent>
                      {MATERIAL_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Fit Type */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Fit Type</label>
              <Controller
                name="fitType"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select fit type" />
                    </SelectTrigger>
                    <SelectContent>
                      {FIT_TYPE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Care Instructions */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Care Instructions</label>
              <Controller
                name="care"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="e.g., Machine wash cold, Hand wash only"
                  />
                )}
              />
            </div>

            {/* Origin */}
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">Origin</label>
              <Controller
                name="origin"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="e.g., Ghana, Made in Ghana"
                  />
                )}
              />
            </div>
          </div>

          {/* Season */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Season <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2 mb-2">
              <Select value={newSeason} onValueChange={setNewSeason}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select season" />
                </SelectTrigger>
                <SelectContent>
                  {SEASON_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={() => {
                  addSeason(newSeason);
                  setNewSeason("");
                }}
                disabled={!newSeason}
                size="sm"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {watch('season')?.map((season, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {season}
                  <button
                    type="button"
                    onClick={() => removeSeason(index)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
            {errors.season && (
              <p className="text-xs text-red-600 mt-1">{errors.season.message}</p>
            )}
          </div>

          {/* Occasion */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Occasion <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2 mb-2">
              <Select value={newOccasion} onValueChange={setNewOccasion}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select occasion" />
                </SelectTrigger>
                <SelectContent>
                  {OCCASION_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                onClick={() => {
                  addOccasion(newOccasion);
                  setNewOccasion("");
                }}
                disabled={!newOccasion}
                size="sm"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {watch('occasion')?.map((occasion, index) => (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {occasion}
                  <button
                    type="button"
                    onClick={() => removeOccasion(index)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              ))}
            </div>
            {errors.occasion && (
              <p className="text-xs text-red-600 mt-1">{errors.occasion.message}</p>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex justify-end mt-8">
            <Button type="submit" className="px-8">
              Save Changes
            </Button>
          </div>
        </form>
      </div>
    );
  }

  // View Mode
  return (
    <div className="bg-white rounded-md shadow-sm p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-700">Specifications</h3>
        <Button variant="ghost" onClick={toggleEdit}>
          <Edit3 className="h-5 w-5" />
        </Button>
      </div>

      <section className="space-y-3">
        <DetailItem label="Material" value={product.specifications?.material} />
        <DetailItem label="Fit Type" value={product.specifications?.fitType} />
        <DetailItem label="Care Instructions" value={product.specifications?.care} />
        <DetailItem label="Origin" value={product.specifications?.origin} />

        <div className="text-sm">
          <p className="text-gray-500">Season</p>
          {product.specifications?.season && product.specifications.season.length > 0 ? (
            <div className="flex flex-wrap gap-2 mt-1">
              {product.specifications.season.map((season: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {season}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-gray-900">Not specified</p>
          )}
        </div>

        <div className="text-sm">
          <p className="text-gray-500">Occasion</p>
          {product.specifications?.occasion && product.specifications.occasion.length > 0 ? (
            <div className="flex flex-wrap gap-2 mt-1">
              {product.specifications.occasion.map((occasion: string, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {occasion}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-gray-900">Not specified</p>
          )}
        </div>
      </section>
    </div>
  );
};

export default SpecificationsSection;
