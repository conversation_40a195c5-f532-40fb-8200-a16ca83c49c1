"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Eye, ShoppingCart, Heart, Star, TrendingUp, Package } from "lucide-react";

interface MetricsCardProps {
  product: any;
}

const MetricsCard: React.FC<MetricsCardProps> = ({ product }) => {
  // Calculate metrics from product data
  const totalViews = product.metrics?.views || 0;
  const totalOrders = product.metrics?.orders || 0;
  const totalWishlists = product.metrics?.wishlists || 0;
  const averageRating = product.metrics?.averageRating || 0;
  const totalRevenue = product.metrics?.revenue || 0;
  const totalStock = product.variations?.reduce((sum: number, variation: any) => sum + (variation.quantity || 0), 0) || 0;

  const metrics = [
    {
      label: "Total Views",
      value: totalViews.toLocaleString(),
      icon: Eye,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      label: "Orders",
      value: totalOrders.toLocaleString(),
      icon: ShoppingCart,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      label: "Wishlists",
      value: totalWishlists.toLocaleString(),
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
    {
      label: "Rating",
      value: averageRating > 0 ? `${averageRating.toFixed(1)}/5` : "No ratings",
      icon: Star,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
    {
      label: "Revenue",
      value: `GHS ${totalRevenue.toLocaleString()}`,
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      label: "Stock",
      value: totalStock.toLocaleString(),
      icon: Package,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
    },
  ];

  return (
    <Card className="bg-white shadow-sm border border-gray-100">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-700">Product Metrics</h3>
          <div className="text-sm text-gray-500">
            Last updated: {new Date().toLocaleDateString()}
          </div>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {metrics.map((metric, index) => {
            const IconComponent = metric.icon;
            return (
              <div key={index} className="flex flex-col items-center text-center p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow">
                <div className={`p-3 rounded-full ${metric.bgColor} mb-3`}>
                  <IconComponent className={`w-6 h-6 ${metric.color}`} />
                </div>
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Additional insights */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <p className="text-gray-600">Conversion Rate</p>
              <p className="font-semibold text-gray-900">
                {totalViews > 0 ? ((totalOrders / totalViews) * 100).toFixed(2) : 0}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-600">Wishlist Rate</p>
              <p className="font-semibold text-gray-900">
                {totalViews > 0 ? ((totalWishlists / totalViews) * 100).toFixed(2) : 0}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-gray-600">Avg. Order Value</p>
              <p className="font-semibold text-gray-900">
                GHS {totalOrders > 0 ? (totalRevenue / totalOrders).toFixed(2) : 0}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricsCard;
