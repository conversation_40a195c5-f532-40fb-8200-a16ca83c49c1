// Product types and interfaces based on the API structure

export interface ProductVariation {
  color: string;
  size: string;
  quantity: number;
  price: number;
  salePrice?: number;
  saleStartDate?: string;
  saleEndDate?: string;
  images?: string[];
  _id?: string;
  onSale?: boolean;
  currentPrice?: number;
  id?: string;
}

export interface ProductSpecifications {
  material?: string;
  fitType?: string;
  care?: string;
  origin?: string;
  season?: string[];
  occasion?: string[];
  [key: string]: any; // Allow additional dynamic specifications
}

export interface ProductSEO {
  keywords: string[];
}

export interface Product {
  _id: string;
  name: string;
  brand: string;
  description: string;
  highlights: string[];
  gender: 'Men' | 'Women' | 'Unisex';
  basePrice: number;
  images: string[];
  category: string | { _id: string; name: string; description?: string }; // Can be populated or just ID
  relatedCategories: string[];
  tags: string[];
  creator: string;
  sold: number;
  ratingsAverage: number;
  ratingsQuantity: number;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  featured: boolean;
  specifications: ProductSpecifications;
  variations: ProductVariation[];
  seo: ProductSEO;
  createdAt: string;
  updatedAt: string;
  slug: string;
  __v: number;
  totalStock: number;
  availableColors: string[];
  availableSizes: string[];
  minPrice: number;
  maxPrice: number;
  id: string;
}

export interface CreateProductData {
  name: string;
  brand: string;
  description: string;
  basePrice: number;
  category: string;
  gender: 'Men' | 'Women' | 'Unisex';
  productImages: File[];
  highlights: string[];
  tags: string[];
  specifications: ProductSpecifications;
  variations: ProductVariation[];
  relatedCategories?: string[];
}

export interface CreateProductResponse {
  status: string;
  data: {
    product: Product;
  };
}

// Form data interfaces for the multi-step form
export interface ProductInfoFormData {
  name: string;
  brand: string;
  description: string;
  basePrice: number;
  category: string;
  gender: 'Men' | 'Women' | 'Unisex';
  productImages: File[];
  highlights: string[];
  tags: string[];
}

export interface SpecificationsFormData {
  material?: string;
  fitType?: string;
  care?: string;
  origin?: string;
  season: string[];
  occasion: string[];
  [key: string]: any;
}

export interface VariationsFormData {
  variations: ProductVariation[];
  relatedCategories: string[];
}

// Combined form data for the entire product creation process
export interface ProductFormData {
  productInfo: ProductInfoFormData;
  specifications: SpecificationsFormData;
  variations: VariationsFormData;
}

// Gender options
export const GENDER_OPTIONS = [
  { value: 'Men', label: 'Men' },
  { value: 'Women', label: 'Women' },
  { value: 'Unisex', label: 'Unisex' }
] as const;

// Common specification options
export const SEASON_OPTIONS = [
  { value: 'Rainy Season', label: 'Rainy Season' },
  { value: 'Dry Season', label: 'Dry Season' },
  { value: 'All Season', label: 'All Season' }
] as const;

export const OCCASION_OPTIONS = [
  { value: 'Casual', label: 'Casual' },
  { value: 'Formal', label: 'Formal' },
  { value: 'Business', label: 'Business' },
  { value: 'Party', label: 'Party' },
  { value: 'Wedding', label: 'Wedding' },
  { value: 'Sports', label: 'Sports' },
  { value: 'Everyday', label: 'Everyday' },
  { value: 'Special Occasion', label: 'Special Occasion' }
] as const;

export const FIT_TYPE_OPTIONS = [
  { value: 'Slim', label: 'Slim' },
  { value: 'Regular', label: 'Regular' },
  { value: 'Loose', label: 'Loose' },
  { value: 'Oversized', label: 'Oversized' },
  { value: 'Tailored', label: 'Tailored' }
] as const;

export const MATERIAL_OPTIONS = [
  { value: '100% Cotton', label: '100% Cotton' },
  { value: '100% Cotton Denim', label: '100% Cotton Denim' },
  { value: 'Cotton Blend', label: 'Cotton Blend' },
  { value: 'Polyester', label: 'Polyester' },
  { value: 'Wool', label: 'Wool' },
  { value: 'Silk', label: 'Silk' },
  { value: 'Linen', label: 'Linen' },
  { value: 'Leather', label: 'Leather' },
  { value: 'Synthetic', label: 'Synthetic' }
] as const;

// Common size options
export const SIZE_OPTIONS = [
  { value: 'XS', label: 'XS' },
  { value: 'S', label: 'S' },
  { value: 'M', label: 'M' },
  { value: 'L', label: 'L' },
  { value: 'XL', label: 'XL' },
  { value: 'XXL', label: 'XXL' },
  { value: '28', label: '28' },
  { value: '30', label: '30' },
  { value: '32', label: '32' },
  { value: '34', label: '34' },
  { value: '36', label: '36' },
  { value: '38', label: '38' },
  { value: '40', label: '40' },
  { value: '42', label: '42' }
] as const;

// Common color options
export const COLOR_OPTIONS = [
  { value: 'Black', label: 'Black' },
  { value: 'White', label: 'White' },
  { value: 'Blue', label: 'Blue' },
  { value: 'Red', label: 'Red' },
  { value: 'Green', label: 'Green' },
  { value: 'Yellow', label: 'Yellow' },
  { value: 'Pink', label: 'Pink' },
  { value: 'Purple', label: 'Purple' },
  { value: 'Orange', label: 'Orange' },
  { value: 'Brown', label: 'Brown' },
  { value: 'Gray', label: 'Gray' },
  { value: 'Navy', label: 'Navy' },
  { value: 'Beige', label: 'Beige' },
  { value: 'Khaki', label: 'Khaki' }
] as const;
