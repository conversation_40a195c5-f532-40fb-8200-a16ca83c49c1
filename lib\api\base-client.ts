/**
 * Base API Client
 * Provides common functionality for all API clients
 */

import { AuthManager } from './auth-manager';
import { interceptorManager } from './interceptors';
import { API_CONFIG, DEFAULT_HEADERS, HTTP_STATUS } from './config';

export interface RequestConfig extends Omit<RequestInit, 'headers'> {
  timeout?: number;
  retries?: number;
  requiresAuth?: boolean;
  isPublic?: boolean;
  headers?: Record<string, string>;
}

export interface ApiResponse<T = any> {
  status: string;
  data?: T;
  message?: string;
  error?: string;
}

export class BaseApiClient {
  protected baseURL: string;
  protected defaultTimeout: number;
  protected defaultRetries: number;

  constructor(baseURL?: string) {
    this.baseURL = baseURL || API_CONFIG.BASE_URL;
    this.defaultTimeout = API_CONFIG.TIMEOUT;
    this.defaultRetries = API_CONFIG.RETRY_ATTEMPTS;
  }

  /**
   * Main request method with error handling, retries, and authentication
   */
  protected async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      requiresAuth = true,
      isPublic = false,
      ...requestConfig
    } = config;

    // Disable retries for authentication endpoints to prevent multiple login attempts
    const isAuthEndpoint = endpoint.includes('/auth/login') ||
                           endpoint.includes('/auth/register') ||
                           endpoint.includes('/auth/verify-email') ||
                           endpoint.includes('/auth/forgot-password') ||
                           endpoint.includes('/auth/reset-password');

    const finalRetries = isAuthEndpoint ? 0 : retries;

    const url = `${this.baseURL}${endpoint}`;
    const method = requestConfig.method || 'GET';

    // Build headers
    const headers: Record<string, string> = {
      ...DEFAULT_HEADERS,
      ...(requestConfig.headers || {}),
    };

    // Add authentication header if required
    if (requiresAuth && !isPublic) {
      const authHeader = AuthManager.getAuthHeader();
      if (authHeader) {
        headers.Authorization = authHeader;
      }
    }

    const finalConfig: RequestInit = {
      ...requestConfig,
      headers,
    };

    // Create interceptor context
    const context = interceptorManager.createContext(url, method, headers);

    return this.executeWithRetry(url, finalConfig, finalRetries, timeout, context);
  }

  /**
   * Execute request with retry logic
   */
  private async executeWithRetry<T>(
    url: string,
    config: RequestInit,
    retries: number,
    timeout: number,
    context: any
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await this.executeRequest<T>(url, config, timeout, context);
      } catch (error) {
        lastError = error as Error;

        // Process error through interceptors
        const processedError = await interceptorManager.processError(lastError, context);

        // Don't retry on authentication errors or client errors
        if (processedError instanceof Error && (
          processedError.message.includes('401') ||
          processedError.message.includes('403') ||
          processedError.message.includes('400')
        )) {
          throw processedError;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < retries) {
          await this.delay(API_CONFIG.RETRY_DELAY * Math.pow(2, attempt));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Execute single request with timeout
   */
  private async executeRequest<T>(
    url: string,
    config: RequestInit,
    timeout: number,
    context: any
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    // Handle FormData: Remove Content-Type header to let browser set multipart boundary
    const finalConfig = { ...config };
    if (config.body instanceof FormData) {
      const headers = { ...(config.headers as Record<string, string>) };
      delete headers['Content-Type'];
      finalConfig.headers = headers;
      console.log('FormData detected: Removed Content-Type header to let browser handle multipart boundary');
      console.log('Final headers for FormData request:', headers);
      console.log('FormData entries count:', Array.from(config.body.entries()).length);
    }

    try {
      const response = await fetch(url, {
        ...finalConfig,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Process response through interceptors
      const processedResponse = await interceptorManager.processResponse(response, context);

      // Handle authentication errors, but not on login/register endpoints
      if (processedResponse.status === HTTP_STATUS.UNAUTHORIZED) {
        const isAuthEndpoint = url.includes('/auth/login') ||
                               url.includes('/auth/register') ||
                               url.includes('/auth/verify-email') ||
                               url.includes('/auth/forgot-password') ||
                               url.includes('/auth/reset-password');

        if (!isAuthEndpoint) {
          AuthManager.handleAuthFailure();
          throw new Error('Session expired. Please login again.');
        }
        // For auth endpoints, let the error bubble up normally
      }

      console.log(response, processedResponse)

      // Handle other HTTP errors
      if (!processedResponse.ok) {
        const errorData = await processedResponse.json().catch(() => ({}));
        console.log('Error data from API:', errorData);

        // Create an error object that preserves the API response structure
        const error = new Error(errorData.message || `HTTP error! status: ${processedResponse.status}`);
        (error as any).response = {
          status: processedResponse.status,
          data: errorData
        };
        throw error;
      }

      const data = await processedResponse.json();

      // Process successful response through interceptors
      const processedData = await interceptorManager.processSuccess(data, context);

      return processedData;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout');
        }
        throw error;
      }

      throw new Error('An unexpected error occurred');
    }
  }



  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // HTTP method helpers
  async get<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // Public request methods (no authentication required)
  async publicGet<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, { ...config, method: 'GET', isPublic: true });
  }

  async publicPost<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      isPublic: true,
    });
  }

  async publicPatch<T>(endpoint: string, data?: any, config: RequestConfig = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      isPublic: true,
    });
  }

  // Form data request methods (for file uploads)
  async postFormData<T>(endpoint: string, formData: FormData, config: RequestConfig = {}): Promise<T> {
    const { headers, ...restConfig } = config;

    // Remove Content-Type header for FormData (browser will set it with boundary)
    const formDataHeaders: Record<string, string> = headers ? { ...headers } : {};
    if ('Content-Type' in formDataHeaders) {
      delete formDataHeaders['Content-Type'];
    }

    return this.request<T>(endpoint, {
      ...restConfig,
      method: 'POST',
      headers: formDataHeaders,
      body: formData,
    });
  }

  async patchFormData<T>(endpoint: string, formData: FormData, config: RequestConfig = {}): Promise<T> {
    // FormData Content-Type handling is now done in executeRequest
    // This ensures it overrides any default headers like application/json
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: formData,
    });
  }
}
